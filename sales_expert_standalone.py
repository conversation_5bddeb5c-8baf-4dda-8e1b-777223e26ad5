#!/usr/bin/env python3
"""
独立销售专家脚本
基于原有的销售专家功能，提供命令行交互界面
支持默认模板和自定义模板选择
"""

import asyncio
import os
import sys
import json
import warnings
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# 导入必要的库
from dotenv import load_dotenv
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo, ModelCapabilities
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools

# 抑制警告
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["PYTHONWARNINGS"] = "ignore"


class SalesExpertStandalone:
    """独立销售专家类"""
    
    def __init__(self):
        """初始化销售专家"""
        self.load_environment()
        self.default_template = self.get_default_sales_expert_template()
        self.custom_templates_file = Path("sales_expert_templates.json")
        
    def load_environment(self):
        """加载环境变量"""
        load_dotenv(dotenv_path="MCP.env")
        
        # 检查必要的API密钥
        self.tavily_api_key = os.getenv("TAVILY_API_KEY")
        if not self.tavily_api_key:
            raise ValueError("TAVILY_API_KEY环境变量是必需的。请在MCP.env文件中设置它。")
            
        print("✅ 环境变量加载成功")
    
    def get_default_sales_expert_template(self) -> str:
        """获取默认销售专家模板"""
        return """You are a professional enterprise sales expert with access to tavily-search tools.

**Your Analysis Process:**
1. First, analyze the product information to understand the target market and application industries
2. Use tavily-search to find relevant websites in the target country/region
3. Search for different Website Categories as specified below
4. Provide specific website recommendations with real URLs

**Website Categories to Search For:**
- Comprehensive news platforms (general business/industry news)
- Vertical field news (industry-specific news and trends)
- Industry information websites (technical developments, market analysis)
- Industry forums (professional discussions and networking)
- Industry association websites (official organizations and standards)
- Exhibition information websites (trade shows and events)
- Financial news websites (investment and market data)

**Search Strategy:**
1. Use tavily-search with queries like: "{countries} pharmaceutical industry news websites"
2. Use tavily-search with queries like: "{countries} biotechnology industry associations"
3. Use tavily-search with queries like: "{countries} chemical equipment trade publications"
4. Search for each category separately to get comprehensive results

**Important Tool Usage:**
When calling tavily-search, use these exact parameter formats:
- query: "your search query here"
- search_depth: "advanced"
- topic: "general"
- max_results: 10
- include_domains: []
- exclude_domains: []

Note: include_domains and exclude_domains must be empty arrays [], not strings.

**Product Information:**
- Product Name: {product_name}
- Product Details: {product_details}
- Target Country/Region: {countries}

**Important Instructions:**
1. MUST use tavily-search tools to find real, current websites
2. Focus on websites from {countries} only
3. Recommend websites where the target market (users of {product_name}) would look for information
4. Provide actual URLs, not example links
5. Each website should be relevant to industries that use {product_name}

**CRITICAL OUTPUT REQUIREMENTS:**

You MUST provide your final analysis in this EXACT format:

**Website Recommendations for {product_name} in {countries}:**

1. **Comprehensive News Platform**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

2. **Vertical Field News**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

3. **Industry Information Website**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

4. **Industry Forum**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

5. **Industry Association**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

6. **Exhibition Information**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

7. **Financial News**:
   - URL: [Real URL found through search]
   - Description: [Brief description of why this website is relevant]
   - Relevance: [How it helps {product_name} sales in {countries}]

**Search Summary:**
- Total searches conducted: [Number]
- Key industries identified: [List]
- Market insights for {countries}: [Brief insights]

IMPORTANT:
- All URLs must be real and found through tavily-search
- Each recommendation must be specifically relevant to {product_name} in {countries}
- Do not use placeholder or example URLs

Start by using tavily-search to find relevant websites for {product_name} in {countries}.

**IMMEDIATE ACTION REQUIRED:**
Please call the tavily-search tool now with the following query:
Query: "{countries} {product_name} industry websites"

Then continue with additional searches for each website category listed above."""

    def get_model_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取支持的模型配置"""
        return {
            "meta-llama/llama-4-maverick": {
                "provider": "openrouter",
                "api_key_env": "OPENROUTER_API_KEY",
                "base_url": "https://openrouter.ai/api/v1",
                "actual_model_name": "meta-llama/llama-4-maverick",
                "context_window": 500000,
                "max_tokens": 16384,
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,
                "multiple_system_messages": True,
                "description": "Llama 4 Maverick，专为商机分析优化的高性能模型（推荐）"
            },
            "grok-4-0709": {
                "provider": "openrouter",
                "api_key_env": "OPENROUTER_API_KEY",
                "base_url": "https://openrouter.ai/api/v1",
                "actual_model_name": "x-ai/grok-4-0709",
                "context_window": 1000000,
                "max_tokens": 32768,
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,
                "multiple_system_messages": True,
                "description": "xAI Grok-4-0709，最强推理模型（通过OpenRouter）"
            },
            "openai/gpt-4o": {
                "provider": "openrouter",
                "api_key_env": "OPENROUTER_API_KEY",
                "base_url": "https://openrouter.ai/api/v1",
                "actual_model_name": "openai/gpt-4o",
                "context_window": 500000,
                "max_tokens": 16384,
                "vision": True,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,
                "multiple_system_messages": True,
                "description": "OpenAI GPT-4o，多模态能力强（通过OpenRouter）"
            },
            "deepseek-reasoner": {
                "provider": "deepseek",
                "api_key_env": "DEEPSEEK_API_KEY",
                "base_url": "https://api.deepseek.com/v1",
                "actual_model_name": "deepseek-reasoner",
                "context_window": 200000,
                "max_tokens": 8192,
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,
                "multiple_system_messages": True,
                "description": "DeepSeek推理模型，专业的逻辑推理和分析能力（直连）"
            },
            "qwen-max": {
                "provider": "aliyun",
                "api_key_env": "ALIYUN_API_KEY",
                "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "actual_model_name": "qwen-max",
                "context_window": 200000,
                "max_tokens": 8192,
                "vision": True,
                "function_calling": True,
                "json_output": True,
                "structured_output": True,
                "multiple_system_messages": True,
                "description": "阿里云通义千问最强版本，多模态能力强（直连）"
            }
        }

    def get_model_client(self, model_name: str = "meta-llama/llama-4-maverick"):
        """获取模型客户端，支持自动切换备用模型"""
        configs = self.get_model_configs()

        if model_name not in configs:
            model_name = "meta-llama/llama-4-maverick"  # 默认使用Llama 4 Maverick

        # 定义备用模型顺序（按API类型分组）
        openrouter_models = [
            "meta-llama/llama-4-maverick",
            "openai/gpt-4o",
            "grok-4-0709"
        ]

        direct_models = [
            "deepseek-reasoner",
            "qwen-max"
        ]

        # 如果当前模型是OpenRouter模型，优先尝试其他OpenRouter模型
        if model_name in openrouter_models:
            # 将当前模型放在第一位，然后是其他OpenRouter模型，最后是直连模型
            fallback_models = [model_name] + [m for m in openrouter_models if m != model_name] + direct_models
        else:
            # 如果是直连模型，优先尝试直连模型，然后是OpenRouter模型
            fallback_models = [model_name] + [m for m in direct_models if m != model_name] + openrouter_models

        # 尝试每个模型
        for attempt_model in fallback_models:
            if attempt_model not in configs:
                continue

            config = configs[attempt_model]

            # 检查API密钥
            api_key = os.getenv(config["api_key_env"])
            if not api_key:
                print(f"⚠️ {config['api_key_env']} 未配置，跳过 {attempt_model}")
                continue

            try:
                # 创建模型信息
                model_info = ModelInfo(
                    family=config["provider"],
                    name=attempt_model,
                    context_window=config["context_window"],
                    max_tokens=config["max_tokens"],
                    vision=config["vision"],
                    function_calling=config["function_calling"],
                    json_output=config["json_output"],
                    structured_output=config["structured_output"],
                    multiple_system_messages=config["multiple_system_messages"]
                )

                # 创建客户端
                client = OpenAIChatCompletionClient(
                    model=config["actual_model_name"],
                    model_info=model_info,
                    api_key=api_key,
                    base_url=config["base_url"]
                )

                print(f"✅ 已初始化 {config['provider'].upper()} 模型: {attempt_model}")
                return client

            except Exception as e:
                print(f"⚠️ {attempt_model} 初始化失败: {str(e)}")
                continue

        # 如果所有模型都失败
        raise ValueError("所有可用模型都无法初始化。请检查API密钥配置或账户余额。")

    async def initialize_tavily_tools(self, max_retries: int = 3):
        """初始化Tavily搜索工具"""
        tavily_server_params = StdioServerParams(
            command="npx",
            args=["-y", "tavily-mcp@0.2.0"],
            env={"TAVILY_API_KEY": self.tavily_api_key},
        )
        
        for attempt in range(max_retries):
            try:
                print(f"初始化Tavily MCP工具 (尝试 {attempt + 1}/{max_retries})...")
                
                tools = await asyncio.wait_for(
                    mcp_server_tools(tavily_server_params),
                    timeout=300.0  # 5分钟超时
                )
                print("✅ Tavily MCP工具初始化成功")
                return tools
                
            except asyncio.TimeoutError:
                print(f"⚠️ MCP工具初始化超时 (尝试 {attempt + 1})")
                if attempt < max_retries - 1:
                    print("等待30秒后重试...")
                    await asyncio.sleep(30)
                    
            except Exception as e:
                print(f"⚠️ MCP工具初始化失败: {str(e)} (尝试 {attempt + 1})")
                if attempt < max_retries - 1:
                    print("等待30秒后重试...")
                    await asyncio.sleep(30)
        
        print("❌ MCP工具初始化最终失败")
        return None

    def load_custom_templates(self) -> Dict[str, str]:
        """加载自定义模板"""
        if not self.custom_templates_file.exists():
            return {}

        try:
            with open(self.custom_templates_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载自定义模板失败: {e}")
            return {}

    def save_custom_template(self, template_name: str, template_content: str):
        """保存自定义模板"""
        templates = self.load_custom_templates()
        templates[template_name] = template_content

        try:
            with open(self.custom_templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)
            print(f"✅ 模板 '{template_name}' 已保存")
        except Exception as e:
            print(f"❌ 保存模板失败: {e}")

    def format_template(self, template: str, product_name: str, product_details: str,
                       countries: str, time_period: str = "2025年7月", opportunities_count: int = 5) -> str:
        """格式化模板 - 与原系统保持一致的参数处理"""
        print(f"🔧 格式化销售专家提示词: 产品={product_name}, 地区={countries}")

        try:
            # 尝试使用所有可能的参数（与原系统一致）
            formatted_prompt = template.format(
                product_name=product_name,
                product_details=product_details,
                countries=countries,
                time_period=time_period,
                opportunities_count=opportunities_count
            )

            # 验证格式化结果（与原系统一致）
            if not formatted_prompt or len(formatted_prompt) < 100:
                raise ValueError("格式化后的提示词过短")

            # 检查关键元素是否存在（与原系统一致）
            required_elements = ['tavily-search', 'Website Categories', 'Output Format']
            missing_elements = [elem for elem in required_elements if elem not in formatted_prompt]

            if missing_elements:
                print(f"⚠️ 提示词缺少关键元素: {missing_elements}")

            print(f"✅ 提示词格式化成功，长度: {len(formatted_prompt)}")
            return formatted_prompt

        except KeyError as e:
            print(f"⚠️ 模板变量错误: {e}，尝试基础参数")
            # 如果某些参数不存在，只使用基础参数（与原系统一致）
            try:
                return template.format(
                    product_name=product_name,
                    product_details=product_details,
                    countries=countries
                )
            except KeyError:
                # 如果还是失败，返回默认模板（与原系统一致）
                print(f"⚠️ 销售专家模板格式化失败: {e}，使用默认模板")
                return self.get_default_sales_expert_template().format(
                    product_name=product_name,
                    product_details=product_details,
                    countries=countries,
                    time_period=time_period,
                    opportunities_count=opportunities_count
                )

    def display_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🤖 销售专家独立分析工具")
        print("="*60)
        print("1. 使用默认模板进行分析")
        print("2. 使用自定义模板进行分析")
        print("3. 查看默认模板")
        print("4. 管理自定义模板")
        print("5. 退出")
        print("="*60)

    def display_model_menu(self) -> str:
        """显示模型选择菜单"""
        configs = self.get_model_configs()
        print("\n📋 选择AI模型:")
        print("-" * 40)

        models = list(configs.keys())
        for i, (model_name, config) in enumerate(configs.items(), 1):
            print(f"{i}. {model_name} - {config['description']}")

        while True:
            try:
                choice = input(f"\n请选择模型 (1-{len(models)}): ").strip()
                if choice.isdigit():
                    idx = int(choice) - 1
                    if 0 <= idx < len(models):
                        selected_model = models[idx]
                        print(f"✅ 已选择模型: {selected_model}")
                        return selected_model
                print("❌ 无效选择，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                sys.exit(0)

    def get_product_info(self) -> tuple:
        """获取产品信息 - 与原系统保持一致的默认值和处理方式"""
        print("\n📋 请输入产品信息:")
        print("-" * 30)

        # 原系统的默认值
        default_product_name = "化学品合成设备"
        default_product_details = "应用于生物制药的微流"
        default_countries = "美国"
        default_time_period = "2025年7月"
        default_opportunities_count = 5

        try:
            # 产品名称输入（显示默认值提示）
            product_name_input = input(f"产品名称 (默认: {default_product_name}): ").strip()
            product_name = product_name_input if product_name_input else default_product_name

            # 产品详情输入（显示默认值提示）
            product_details_input = input(f"产品详细信息 (默认: {default_product_details}): ").strip()
            product_details = product_details_input if product_details_input else default_product_details

            # 目标国家输入（显示默认值提示）
            countries_input = input(f"目标国家/地区 (默认: {default_countries}): ").strip()
            countries = countries_input if countries_input else default_countries

            # 时间段输入（显示默认值提示）
            time_period_input = input(f"检索时间段 (默认: {default_time_period}): ").strip()
            time_period = time_period_input if time_period_input else default_time_period

            # 商机数量输入（显示默认值提示）
            opportunities_input = input(f"期望商机数量 (默认: {default_opportunities_count}): ").strip()
            try:
                opportunities_count = int(opportunities_input) if opportunities_input else default_opportunities_count
                if opportunities_count < 1 or opportunities_count > 100:
                    print("⚠️ 商机数量超出范围，使用默认值")
                    opportunities_count = default_opportunities_count
            except ValueError:
                print("⚠️ 商机数量格式错误，使用默认值")
                opportunities_count = default_opportunities_count

            print(f"\n✅ 产品信息确认:")
            print(f"   产品名称: {product_name}")
            print(f"   产品详情: {product_details}")
            print(f"   目标地区: {countries}")
            print(f"   时间段: {time_period}")
            print(f"   商机数量: {opportunities_count}")

            return product_name, product_details, countries, time_period, opportunities_count

        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return None, None, None, None, None

    def manage_custom_templates(self):
        """管理自定义模板"""
        while True:
            print("\n📋 自定义模板管理:")
            print("-" * 30)
            print("1. 查看已保存的模板")
            print("2. 创建新模板")
            print("3. 删除模板")
            print("4. 返回主菜单")

            try:
                choice = input("\n请选择操作 (1-4): ").strip()

                if choice == "1":
                    self.list_custom_templates()
                elif choice == "2":
                    self.create_custom_template()
                elif choice == "3":
                    self.delete_custom_template()
                elif choice == "4":
                    break
                else:
                    print("❌ 无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n👋 返回主菜单")
                break

    def list_custom_templates(self):
        """列出自定义模板"""
        templates = self.load_custom_templates()
        if not templates:
            print("📝 暂无自定义模板")
            return

        print("\n📝 已保存的自定义模板:")
        print("-" * 40)
        for i, (name, template) in enumerate(templates.items(), 1):
            preview = template[:100] + "..." if len(template) > 100 else template
            print(f"{i}. {name}")
            print(f"   预览: {preview}")
            print()

    def create_custom_template(self):
        """创建自定义模板"""
        try:
            template_name = input("\n模板名称: ").strip()
            if not template_name:
                print("❌ 模板名称不能为空")
                return

            print("\n请输入模板内容 (输入 'END' 结束输入):")
            print("提示: 可以使用 {product_name}, {product_details}, {countries} 作为变量")
            print("-" * 50)

            template_lines = []
            while True:
                line = input()
                if line.strip() == "END":
                    break
                template_lines.append(line)

            template_content = "\n".join(template_lines)
            if not template_content.strip():
                print("❌ 模板内容不能为空")
                return

            self.save_custom_template(template_name, template_content)

        except KeyboardInterrupt:
            print("\n👋 取消创建模板")

    def delete_custom_template(self):
        """删除自定义模板"""
        templates = self.load_custom_templates()
        if not templates:
            print("📝 暂无自定义模板可删除")
            return

        print("\n选择要删除的模板:")
        template_names = list(templates.keys())
        for i, name in enumerate(template_names, 1):
            print(f"{i}. {name}")

        try:
            choice = input(f"\n请选择 (1-{len(template_names)}): ").strip()
            if choice.isdigit():
                idx = int(choice) - 1
                if 0 <= idx < len(template_names):
                    template_name = template_names[idx]
                    confirm = input(f"确认删除模板 '{template_name}'? (y/N): ").strip().lower()
                    if confirm == 'y':
                        del templates[template_name]
                        with open(self.custom_templates_file, 'w', encoding='utf-8') as f:
                            json.dump(templates, f, ensure_ascii=False, indent=2)
                        print(f"✅ 模板 '{template_name}' 已删除")
                    else:
                        print("❌ 取消删除")
                else:
                    print("❌ 无效选择")
            else:
                print("❌ 无效选择")

        except KeyboardInterrupt:
            print("\n👋 取消删除操作")

    async def run_analysis(self, template: str, product_name: str, product_details: str,
                          countries: str, model_name: str, time_period: str = "2025年7月",
                          opportunities_count: int = 5):
        """运行销售专家分析"""
        print("\n🚀 开始销售专家分析...")
        print("="*50)

        try:
            # 初始化模型客户端（支持自动切换）
            print("🔧 初始化AI模型...")
            try:
                model_client = self.get_model_client(model_name)
            except ValueError as e:
                if "配额" in str(e) or "quota" in str(e).lower():
                    print("⚠️ 检测到配额问题，尝试切换到备用模型...")
                    # 尝试使用其他可用模型
                    backup_models = ["openai/gpt-4o", "grok-4-0709", "deepseek-reasoner"]
                    for backup_model in backup_models:
                        try:
                            print(f"🔄 尝试使用备用模型: {backup_model}")
                            model_client = self.get_model_client(backup_model)
                            model_name = backup_model  # 更新使用的模型名称
                            break
                        except:
                            continue
                    else:
                        raise ValueError("所有模型都无法使用，请检查API密钥配置")
                else:
                    raise e

            # 初始化搜索工具
            print("🔧 初始化搜索工具...")
            tools = await self.initialize_tavily_tools()
            if not tools:
                print("❌ 搜索工具初始化失败，无法进行分析")
                return

            # 格式化模板
            print("🔧 准备分析任务...")
            formatted_template = self.format_template(template, product_name, product_details, countries, time_period, opportunities_count)

            # 创建销售专家Agent
            print("🤖 创建销售专家Agent...")
            sales_expert = AssistantAgent(
                name="sales_expert",
                model_client=model_client,
                system_message=formatted_template,
                tools=tools,
                description="专业的销售专家，能够分析产品信息并推荐相关的网站和搜索关键词"
            )

            # 执行分析
            print("🎯 开始执行分析...")
            print(f"📋 产品: {product_name}")
            print(f"📋 目标地区: {countries}")
            print("-" * 50)

            # 创建明确的分析任务 - 确保AI完成完整分析
            analysis_task = f"""请严格按照你的系统指令执行完整的销售专家分析。

产品信息：
- 产品名称：{product_name}
- 产品详情：{product_details}
- 目标地区：{countries}
- 时间段：{time_period}
- 期望商机数量：{opportunities_count}

执行要求：
1. 必须使用tavily-search工具搜索以下7个网站类别：
   - Comprehensive news platforms
   - Vertical field news
   - Industry information websites
   - Industry forums
   - Industry association websites
   - Exhibition information websites
   - Financial news websites

2. 对每个类别执行独立的搜索查询

3. 必须提供完整的最终分析结果，格式如下：

**Website Recommendations for {product_name} in {countries}:**

1. **Comprehensive News Platform**:
   - URL: [通过搜索找到的真实URL]
   - Description: [网站描述]
   - Relevance: [与{product_name}在{countries}的相关性]

[继续完成所有7个类别的推荐]

**Search Summary:**
- Total searches conducted: [搜索次数]
- Key industries identified: [识别的关键行业]
- Market insights for {countries}: [市场洞察]

重要：必须完成完整的分析流程，不要只执行搜索工具调用。"""

            # 执行分析，设置超时
            print("⏳ 正在分析中，请稍候...")
            print("💡 AI正在严格按照提示词要求执行完整分析...")

            result = await asyncio.wait_for(
                sales_expert.run(task=analysis_task),
                timeout=900.0  # 15分钟超时，给AI更多时间完成完整分析
            )

            # 处理结果
            print("\n✅ 分析完成！")
            print("="*50)

            if hasattr(result, 'messages') and result.messages:
                print(f"📊 收到 {len(result.messages)} 条消息")

                # 提取最终分析结果
                final_result = ""
                for message in result.messages:
                    if hasattr(message, 'content'):
                        content = str(message.content).strip()
                        if content and not content.startswith('[FunctionCall') and not content.startswith('[FunctionExecutionResult'):
                            if len(content) > 100:  # 过滤掉太短的消息
                                final_result = content

                if final_result:
                    print("\n📋 销售专家分析结果:")
                    print("="*50)
                    print(final_result)
                    print("="*50)

                    # 保存结果到文件
                    self.save_analysis_result(product_name, countries, final_result)
                else:
                    print("⚠️ 未能提取到有效的分析结果")
            else:
                print("⚠️ 未收到有效的分析结果")

        except asyncio.TimeoutError:
            print("❌ 分析超时，请稍后重试")
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {str(e)}")

    def extract_and_validate_result(self, messages, product_name: str, countries: str) -> str:
        """提取和验证分析结果"""
        print("🔍 提取分析结果...")

        # 收集所有有效的消息内容
        all_content = []
        tool_calls_count = 0
        analysis_content = []

        for message in messages:
            if hasattr(message, 'content'):
                content = str(message.content).strip()

                # 统计工具调用
                if '[FunctionCall' in content or 'tavily-search' in content:
                    tool_calls_count += 1

                # 分类处理不同类型的内容
                if content.startswith('[FunctionCall') or content.startswith('[FunctionExecutionResult'):
                    # 跳过工具调用和执行结果
                    continue
                elif len(content) > 100:
                    # 保留长内容（可能是分析结果）
                    all_content.append(content)

                    # 检查是否包含分析结构
                    if ('Website Recommendations' in content or
                        'Comprehensive News Platform' in content or
                        'URL:' in content or
                        'Description:' in content or
                        '**' in content):  # 包含格式化标记
                        analysis_content.append(content)

        print(f"📊 检测到 {tool_calls_count} 次工具调用")
        print(f"📊 提取到 {len(all_content)} 条有效内容")
        print(f"📊 发现 {len(analysis_content)} 条分析内容")

        # 优先选择包含分析结构的内容
        if analysis_content:
            final_result = max(analysis_content, key=len)
            print("✅ 找到结构化分析结果")
        elif all_content:
            final_result = max(all_content, key=len)
            print("⚠️ 使用最长内容作为结果")
        else:
            print("❌ 未找到有效的分析内容")
            # 如果没有找到分析内容，但有工具调用，说明AI执行了搜索但没有完成分析
            if tool_calls_count > 0:
                return f"⚠️ AI执行了 {tool_calls_count} 次搜索，但未完成完整分析。\n建议：\n1. 检查提示词是否明确要求完整输出\n2. 尝试使用不同的AI模型\n3. 增加分析超时时间"
            else:
                return "❌ AI未执行任何搜索工具调用，分析失败。"

        # 验证结果质量
        if len(final_result) < 200:
            print("⚠️ 分析结果过短，可能不完整")

        if tool_calls_count == 0:
            print("⚠️ 未检测到工具调用，AI可能没有执行搜索")

        return final_result

    def validate_result_quality(self, result: str, product_name: str, countries: str) -> int:
        """验证结果质量，返回1-10的评分"""
        score = 0

        # 基础检查（2分）
        if len(result) > 200:
            score += 1
        if len(result) > 1000:
            score += 1

        # 关键信息检查（2分）
        if product_name.lower() in result.lower():
            score += 1
        if countries.lower() in result.lower():
            score += 1

        # URL检查（2分）
        import re
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', result)
        if len(urls) >= 3:
            score += 1
        if len(urls) >= 7:
            score += 1

        # 结构化检查（2分）
        if '**' in result or '##' in result:  # 包含标题格式
            score += 1
        if 'URL:' in result or 'Description:' in result:  # 包含结构化信息
            score += 1

        # 搜索工具使用检查（2分）
        if 'search' in result.lower() or 'found' in result.lower():
            score += 1
        if len(urls) > 0:  # 实际找到了URL
            score += 1

        return min(score, 10)

    def save_analysis_result(self, product_name: str, countries: str, result: str,
                           time_period: str = "", opportunities_count: int = 0):
        """保存分析结果到文件"""
        try:
            # 创建结果目录
            results_dir = Path("analysis_results")
            results_dir.mkdir(exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sales_analysis_{product_name}_{countries}_{timestamp}.txt"
            filename = filename.replace(" ", "_").replace("/", "_")

            filepath = results_dir / filename

            # 保存结果
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"销售专家分析结果\n")
                f.write(f"="*50 + "\n")
                f.write(f"产品名称: {product_name}\n")
                f.write(f"目标地区: {countries}\n")
                f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"="*50 + "\n\n")
                f.write(result)

            print(f"💾 分析结果已保存到: {filepath}")

        except Exception as e:
            print(f"⚠️ 保存结果失败: {e}")

    def select_custom_template(self) -> Optional[str]:
        """选择自定义模板"""
        templates = self.load_custom_templates()
        if not templates:
            print("📝 暂无自定义模板，请先创建")
            return None

        print("\n📋 选择自定义模板:")
        print("-" * 30)
        template_names = list(templates.keys())
        for i, name in enumerate(template_names, 1):
            print(f"{i}. {name}")

        try:
            choice = input(f"\n请选择模板 (1-{len(template_names)}): ").strip()
            if choice.isdigit():
                idx = int(choice) - 1
                if 0 <= idx < len(template_names):
                    template_name = template_names[idx]
                    print(f"✅ 已选择模板: {template_name}")
                    return templates[template_name]
            print("❌ 无效选择")
            return None

        except KeyboardInterrupt:
            print("\n👋 取消选择")
            return None

    async def run_default_analysis(self):
        """使用默认模板进行分析"""
        print("\n🎯 使用默认模板进行分析")

        # 选择模型
        model_name = self.display_model_menu()

        # 获取产品信息
        product_info = self.get_product_info()
        if not product_info or None in product_info:
            return

        product_name, product_details, countries, time_period, opportunities_count = product_info

        # 运行分析
        await self.run_analysis(self.default_template, product_name, product_details, countries, model_name, time_period, opportunities_count)

    async def run_custom_analysis(self):
        """使用自定义模板进行分析"""
        print("\n🎯 使用自定义模板进行分析")

        # 选择自定义模板
        template = self.select_custom_template()
        if not template:
            return

        # 选择模型
        model_name = self.display_model_menu()

        # 获取产品信息
        product_info = self.get_product_info()
        if not product_info or None in product_info:
            return

        product_name, product_details, countries, time_period, opportunities_count = product_info

        # 运行分析
        await self.run_analysis(template, product_name, product_details, countries, model_name, time_period, opportunities_count)

    def view_default_template(self):
        """查看默认模板"""
        print("\n📋 默认销售专家模板:")
        print("="*60)
        print(self.default_template)
        print("="*60)
        input("\n按回车键返回主菜单...")

    async def main_loop(self):
        """主循环"""
        print("🎉 欢迎使用销售专家独立分析工具！")

        while True:
            try:
                self.display_menu()
                choice = input("\n请选择操作 (1-5): ").strip()

                if choice == "1":
                    await self.run_default_analysis()
                elif choice == "2":
                    await self.run_custom_analysis()
                elif choice == "3":
                    self.view_default_template()
                elif choice == "4":
                    self.manage_custom_templates()
                elif choice == "5":
                    print("👋 感谢使用，再见！")
                    break
                else:
                    print("❌ 无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n👋 用户取消操作，退出程序")
                break
            except Exception as e:
                print(f"❌ 程序出现错误: {str(e)}")
                print("请重试或联系技术支持")


async def main():
    """主函数"""
    try:
        # 创建销售专家实例
        sales_expert = SalesExpertStandalone()

        # 运行主循环
        await sales_expert.main_loop()

    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("请检查 MCP.env 文件中的API密钥配置")
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        print("请检查环境配置和依赖库安装")


if __name__ == "__main__":
    # 运行程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行失败: {str(e)}")
        sys.exit(1)
