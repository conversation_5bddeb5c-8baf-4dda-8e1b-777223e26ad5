#!/usr/bin/env python3
"""
API配额问题解决脚本
帮助用户诊断和解决API配额不足的问题
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def check_api_quotas():
    """检查各个API的配额状态"""
    print("🔍 检查API配额状态...")
    print("="*50)
    
    # 加载环境变量
    load_dotenv(dotenv_path="MCP.env")
    
    api_configs = {
        "OPENROUTER_API_KEY": {
            "name": "OpenRouter (Llama 4 Maverick)",
            "check_url": "https://openrouter.ai/account",
            "priority": 1
        },
        "OPENAI_API_KEY": {
            "name": "OpenAI (GPT-4o)",
            "check_url": "https://platform.openai.com/usage",
            "priority": 2
        },
        "GROK_API_KEY": {
            "name": "xAI Grok",
            "check_url": "https://console.x.ai/",
            "priority": 3
        },
        "DEEPSEEK_API_KEY": {
            "name": "DeepSeek",
            "check_url": "https://platform.deepseek.com/usage",
            "priority": 4
        },
        "ALIYUN_API_KEY": {
            "name": "阿里云通义千问",
            "check_url": "https://dashscope.console.aliyun.com/",
            "priority": 5
        }
    }
    
    available_apis = []
    
    for key, config in api_configs.items():
        api_key = os.getenv(key)
        if api_key and len(api_key.strip()) > 10:
            print(f"✅ {config['name']}: 已配置")
            available_apis.append((key, config))
        else:
            print(f"❌ {config['name']}: 未配置")
    
    print(f"\n📊 可用API数量: {len(available_apis)}")
    
    if not available_apis:
        print("⚠️ 没有配置任何API密钥！")
        return False
    
    return available_apis

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决API配额问题的方案:")
    print("="*50)
    
    print("🎯 立即可用的解决方案:")
    print("1. 使用其他已配置的API")
    print("2. 检查和充值现有账户")
    print("3. 注册新的免费API账户")
    
    print("\n📋 推荐的免费/低成本API:")
    print("1. OpenAI GPT-4o - 新用户有免费额度")
    print("2. DeepSeek - 提供免费API额度")
    print("3. 阿里云通义千问 - 新用户有免费试用")
    
    print("\n🔧 配置新API的步骤:")
    print("1. 注册账户并获取API密钥")
    print("2. 在 MCP.env 文件中添加密钥")
    print("3. 重新运行销售专家工具")

def create_backup_config():
    """创建备用配置建议"""
    print("\n📝 创建备用配置...")
    
    backup_config = """# 备用API配置建议
# 将以下内容添加到 MCP.env 文件中

# 必需 - Tavily搜索API (免费额度)
TAVILY_API_KEY=your_tavily_api_key_here

# 备用方案1 - OpenAI (新用户有免费额度)
OPENAI_API_KEY=your_openai_api_key_here

# 备用方案2 - DeepSeek (提供免费API)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 备用方案3 - 阿里云通义千问 (新用户免费试用)
ALIYUN_API_KEY=your_aliyun_api_key_here

# 原配置 - OpenRouter (如果有余额)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 可选 - xAI Grok
GROK_API_KEY=your_grok_api_key_here
"""
    
    try:
        with open("MCP_backup.env", "w", encoding="utf-8") as f:
            f.write(backup_config)
        print("✅ 备用配置已保存到: MCP_backup.env")
        print("💡 请根据需要修改并重命名为 MCP.env")
    except Exception as e:
        print(f"❌ 保存备用配置失败: {e}")

def test_available_models():
    """测试可用的模型"""
    print("\n🧪 测试可用模型...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, str(Path(__file__).parent))
        from sales_expert_standalone import SalesExpertStandalone
        
        sales_expert = SalesExpertStandalone()
        configs = sales_expert.get_model_configs()
        
        print("📋 尝试初始化各个模型:")
        
        for model_name, config in configs.items():
            api_key = os.getenv(config["api_key_env"])
            if api_key:
                try:
                    client = sales_expert.get_model_client(model_name)
                    print(f"✅ {model_name}: 可用")
                except Exception as e:
                    if "quota" in str(e).lower() or "配额" in str(e):
                        print(f"⚠️ {model_name}: 配额不足")
                    else:
                        print(f"❌ {model_name}: {str(e)[:50]}...")
            else:
                print(f"⚠️ {model_name}: 未配置API密钥")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def quick_fix_guide():
    """快速修复指南"""
    print("\n🚀 快速修复指南:")
    print("="*50)
    
    print("📋 如果您想立即继续使用:")
    print("1. 检查其他API是否可用:")
    print("   python fix_quota_issue.py")
    
    print("\n2. 使用备用模型运行:")
    print("   python sales_expert_standalone.py")
    print("   # 选择 openai/gpt-4o 或其他可用模型")
    
    print("\n3. 或使用快速模式:")
    print("   python run_sales_expert.py quick")
    print("   # 系统会自动尝试可用的模型")
    
    print("\n📋 长期解决方案:")
    print("1. 充值现有API账户")
    print("2. 注册多个API服务作为备用")
    print("3. 监控API使用量，避免超额")

def main():
    """主函数"""
    print("🔧 API配额问题诊断和修复工具")
    print("="*60)
    
    # 检查API配额
    available_apis = check_api_quotas()
    
    # 测试可用模型
    if available_apis:
        test_available_models()
    
    # 提供解决方案
    provide_solutions()
    
    # 创建备用配置
    create_backup_config()
    
    # 快速修复指南
    quick_fix_guide()
    
    print("\n" + "="*60)
    print("💡 建议: 配置多个API作为备用，避免单点故障")
    print("📞 如需帮助，请查看 使用技巧.md 文件")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
        sys.exit(1)
